import {sql} from "drizzle-orm";
import postgres from "postgres";
import {drizzle} from "drizzle-orm/postgres-js";
import {config} from "dotenv";

config({ path: '.env' });

const createUserTriggerFunction = sql`
  CREATE OR REPLACE FUNCTION public.handle_new_user()
  RETURNS trigger
  LANGUAGE plpgsql
  SECURITY DEFINER SET search_path = public
  AS $$
  BEGIN
    insert into public.user_info (id, primary_email, display_name)
    values (new.id, new.raw_user_meta_data->>'primaryEmail', new.raw_user_meta_data->>'displayName');
    return new;
  END;
  $$;
`;

const createUserTrigger = sql`
  DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
  
  CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
`;

const main = async () => {
  const client = postgres(process.env.DATABASE_URL!);
  const db = drizzle({ client });

  console.log("Starting post scripts...");

  try {
    console.log("Creating user trigger function...");
    await db.execute(createUserTriggerFunction);
    console.log("Creating user trigger...");
    await db.execute(createUserTrigger);
    console.log("Post scripts completed successfully!");
  } catch (error) {
    console.error("❌ Post scripts failed:", error);
    process.exit(1);
  } finally {
    await client.end();
  }

  // Execute trigger function
  await db.execute(createUserTriggerFunction);

  // Execute trigger
  await db.execute(createUserTrigger);
};

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});