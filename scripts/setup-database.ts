import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { setupDatabase } from '../src/db/schema';

config({ path: '.env' });

async function main() {
  console.log('Setting up database with custom SQL...');
  
  const client = postgres(process.env.DATABASE_URL!);
  const db = drizzle({ client });
  
  try {
    await setupDatabase(db);
    console.log('✅ Database setup completed successfully!');
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

main();
